#!/usr/bin/env python3
"""
Enhanced version of probedesign_codonaln_genomad that uses the NeLLi genome database
for retrieving viral genomes instead of requiring pre-downloaded files.
"""

import os
import csv
import shutil
import subprocess
import re
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
from Bio.Seq import Seq
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import logging
import typer
from typing import Optional, List, Set
from pathlib import Path

# Import our genome database module
from .genome_database import GenomeDatabase

app = typer.Typer()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("pipeline.log"),  # Log to file
        logging.StreamHandler()               # Also log to console
    ]
)


def query_and_download_genomes(
    taxonomy: str,
    output_dir: str,
    limit: Optional[int] = None,
    tax_level: Optional[str] = "species",
    threads: int = 5
) -> tuple[List[str], List[str]]:
    """
    Query the genome database and download matching genomes.
    
    Args:
        taxonomy: Taxonomy string to search for
        output_dir: Directory to save downloaded genomes
        limit: Maximum number of genomes to download
        tax_level: Taxonomic level for grouping (species, genus, etc.)
        threads: Number of parallel download threads
        
    Returns:
        Tuple of (fna_files, faa_files) paths
    """
    logging.info(f"Querying genome database for taxonomy: {taxonomy}")
    
    # Initialize database connection
    db = GenomeDatabase()
    
    # Query genomes
    genomes_df = db.query_by_taxonomy(
        taxonomy=taxonomy,
        tax_level=tax_level,
        limit=limit,
        max_genome_size_mb=100  # Reasonable limit for viral genomes
    )
    
    if genomes_df.empty:
        logging.warning(f"No genomes found for taxonomy: {taxonomy}")
        return [], []
    
    logging.info(f"Found {len(genomes_df)} genomes matching criteria")
    
    # Set up prefix rules for viral genomes
    prefix_rules = {
        "Nucleocytoviricota": "NCLDV__",
        "Faserviricetes": "FASER__",
        "Caudoviricetes": "CAUDV__",
        "default": "VIR__"
    }
    
    # Download genomes
    downloaded_files = db.download_genomes(
        genomes_df=genomes_df,
        output_dir=output_dir,
        download_fna=True,
        download_faa=True,
        threads=threads,
        prefix_rules=prefix_rules
    )
    
    return downloaded_files['fna'], downloaded_files['faa']


def collect_matching_seq_names_from_genomad(
    tsv_path: str, 
    target_taxonomy: str, 
    use_regex: bool = False, 
    limit: Optional[int] = None
) -> Set[str]:
    """
    Read the GeNomad TSV file and collect sequence names where the taxonomy matches.
    This is kept for compatibility with existing GeNomad outputs.
    """
    logging.info(f"Reading TSV file: {tsv_path}")
    matching_seq_names = set()
    sequences_collected = 0

    with open(tsv_path, 'r', newline='', encoding='utf-8') as tsvfile:
        reader = csv.reader(tsvfile, delimiter='\t')
        header = next(reader, None)
        if header is None:
            raise ValueError("The TSV file is empty or missing a header.")

        taxonomy_idx = len(header) - 1  # Assuming taxonomy is the last column
        seq_name_idx = 0  # Assuming sequence name is the first column

        if use_regex:
            try:
                pattern = re.compile(target_taxonomy)
                logging.info(f"Using regex pattern for matching: '{target_taxonomy}'")
            except re.error as e:
                logging.error(f"Invalid regex pattern: {e}")
                raise typer.Exit(code=1)
        else:
            logging.info(f"Filtering sequences with exact taxonomy match: '{target_taxonomy}'")

        for row_num, row in enumerate(reader, start=2):
            if len(row) < taxonomy_idx + 1:
                logging.warning(f"Row {row_num} is incomplete. Skipping.")
                continue
            taxonomy = row[taxonomy_idx].strip()
            match = False
            if use_regex:
                if pattern.search(taxonomy):
                    match = True
            else:
                if taxonomy == target_taxonomy:
                    match = True

            if match:
                seq_name = row[seq_name_idx].strip()
                if seq_name not in matching_seq_names:
                    matching_seq_names.add(seq_name)
                    sequences_collected += 1
                    if limit is not None and sequences_collected >= limit:
                        logging.info(f"Reached the specified limit of {limit} sequences.")
                        break

    logging.info(f"Found {len(matching_seq_names)} matching sequences.")
    return matching_seq_names


@app.command()
def main(
    tsv_path: Optional[str] = typer.Option(None, "-t", "--tsv", 
        help="Path to the GeNomad taxonomy TSV file (optional if using --download-genomes)"),
    fasta_path: Optional[str] = typer.Option(None, "-f", "--fna", 
        help="Path to the input FASTA file (optional if using --download-genomes)"),
    taxonomy: str = typer.Option(..., "-x", "--taxonomy", 
        help="Taxonomy string to filter by (e.g., 'Faserviricetes')"),
    conservation_level: float = typer.Option(0.3, "-c", "--conservation", 
        help="Conservation level for orthogroup filtering"),
    threads: int = typer.Option(4, "-j", "--threads", 
        help="Number of threads for parallel processing"),
    use_regex: bool = typer.Option(False, "-r", "--regex", 
        help="Enable regex-based taxonomy matching"),
    n: Optional[int] = typer.Option(None, "-n", "--number", 
        help="Limit the number of genomes to process"),
    output_dir: str = typer.Option(None, "-o", "--output-dir",
        help="Output directory name (default: results_<taxonomy>)"),
    download_genomes: bool = typer.Option(False, "-d", "--download-genomes",
        help="Download genomes from database instead of using local files"),
    tax_level: str = typer.Option("species", "--tax-level",
        help="Taxonomic level for genome selection when downloading (species, genus, family)")
):
    """
    Enhanced pipeline that can either:
    1. Process local GeNomad output files (traditional mode)
    2. Download genomes directly from the database (--download-genomes mode)
    """
    
    # Determine output directory
    if output_dir is None:
        taxonomy_safe = taxonomy.replace(';', '-').replace(' ', '_').replace('/', '_')
        output_dir = f"results_{taxonomy_safe}"
    
    # Create main output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Mode 1: Download genomes from database
    if download_genomes:
        logging.info("Mode: Downloading genomes from database")
        
        # Create data subdirectory
        data_dir = os.path.join(output_dir, "data")
        os.makedirs(data_dir, exist_ok=True)
        
        # Query and download genomes
        fna_files, faa_files = query_and_download_genomes(
            taxonomy=taxonomy,
            output_dir=data_dir,
            limit=n,
            tax_level=tax_level,
            threads=threads
        )
        
        if not fna_files:
            logging.error("No genomes downloaded. Exiting.")
            raise typer.Exit(code=1)
        
        # Set up directories for pipeline
        fna_dir = os.path.join(data_dir, "fna")
        faa_dir = os.path.join(data_dir, "faa") 
        fnn_dir = os.path.join(output_dir, "fnn")
        
        # We already have FAA files, but we need FNN files
        # Run prodigal-gv to generate FNN files
        logging.info("Generating nucleotide gene sequences (FNN files)")
        os.makedirs(fnn_dir, exist_ok=True)
        
        # Process each genome to get FNN files
        for fna_file in tqdm(fna_files, desc="Generating FNN files"):
            base_name = os.path.basename(fna_file).replace('.fna', '')
            fnn_output = os.path.join(fnn_dir, f"{base_name}.fnn")
            
            cmd = [
                "prodigal-gv",
                "-i", fna_file,
                "-d", fnn_output,
                "-p", "meta",
                "-q"
            ]
            
            try:
                subprocess.run(cmd, check=True, capture_output=True)
            except subprocess.CalledProcessError as e:
                logging.error(f"prodigal-gv failed on {fna_file}: {e.stderr.decode()}")
                
    # Mode 2: Process local GeNomad output
    else:
        logging.info("Mode: Processing local GeNomad output files")
        
        if not tsv_path or not fasta_path:
            logging.error("--tsv and --fna are required when not using --download-genomes")
            raise typer.Exit(code=1)
        
        # Collect matching sequences from GeNomad output
        matching_seq_names = collect_matching_seq_names_from_genomad(
            tsv_path, taxonomy, use_regex, limit=n
        )
        
        if not matching_seq_names:
            logging.error("No matching sequences found. Exiting.")
            raise typer.Exit(code=1)
        
        # Filter and write .fna files
        fna_dir = os.path.join(output_dir, "fna")
        filter_and_write_fasta_sequences(fasta_path, matching_seq_names, fna_dir)
        
        # Run Prodigal on all .fna files
        faa_dir = os.path.join(output_dir, "faa")
        fnn_dir = os.path.join(output_dir, "fnn")
        prodigal_success = run_prodigal_parallel(fna_dir, faa_dir, fnn_dir, threads)
        
        if not prodigal_success:
            logging.error("Prodigal failed on some or all .fna files. Aborting pipeline.")
            raise typer.Exit(code=1)
        
        # Adjust Prodigal output headers
        run_prodigal_adjust_headers(faa_dir, fnn_dir)
    
    # Common pipeline steps for both modes
    # Step 5: Run Proteinortho
    proteinortho_dir = os.path.join(output_dir, "proteinortho")
    os.makedirs(proteinortho_dir, exist_ok=True)
    
    run_proteinortho(proteinortho_dir, output_dir, faa_dir, threads)
    
    # Step 6: Parse Proteinortho Output
    proteinortho_tsv = os.path.join(proteinortho_dir, f"{os.path.basename(output_dir)}.proteinortho.tsv")
    orthogroups_file = os.path.join(proteinortho_dir, f"{os.path.basename(output_dir)}.proteinortho.groups")
    
    if not os.path.exists(proteinortho_tsv):
        logging.error(f"Proteinortho TSV file '{proteinortho_tsv}' not found.")
        raise typer.Exit(code=1)
    
    try:
        create_orthogroups_file(proteinortho_tsv, orthogroups_file)
    except Exception as e:
        logging.error(f"Error creating orthogroups file: {e}")
        raise typer.Exit(code=1)
    
    # Step 7: Filter Orthogroups by Conservation
    # Count total genomes
    total_genomes = len([f for f in os.listdir(faa_dir) if f.endswith('.faa')])
    
    qualifying_orthogroups = filter_orthogroups_by_conservation(
        orthogroups_file, conservation_level, total_genomes
    )
    logging.info(f"Found {len(qualifying_orthogroups)} orthogroups meeting conservation level {conservation_level}")
    
    if conservation_level > 0 and qualifying_orthogroups:
        conserved_faa_dir = os.path.join(output_dir, "conserved_faa")
        conserved_fnn_dir = os.path.join(output_dir, "conserved_fnn")
        os.makedirs(conserved_faa_dir, exist_ok=True)
        os.makedirs(conserved_fnn_dir, exist_ok=True)
        
        # Create Multi-FAA and FNN Files
        try:
            create_multi_faa_fnn_files(
                qualifying_orthogroups, orthogroups_file, 
                faa_dir, fnn_dir, conserved_faa_dir, conserved_fnn_dir, threads
            )
            logging.info(f"Multi-FAA and FNN files created")
        except Exception as e:
            logging.error(f"Error creating multi-FAA and FNN files: {e}")
            raise typer.Exit(code=1)
        
        # Align with MAFFT
        alignment_dir = os.path.join(output_dir, "alignments")
        os.makedirs(alignment_dir, exist_ok=True)
        try:
            align_all_faa_files(conserved_faa_dir, alignment_dir, threads)
            logging.info(f"Alignments created")
        except Exception as e:
            logging.error(f"Error creating alignments: {e}")
            raise typer.Exit(code=1)
        
        # Create Codon Alignments
        codon_dir = os.path.join(output_dir, "codon_alignments")
        os.makedirs(codon_dir, exist_ok=True)
        try:
            create_codon_alignments_for_orthogroups(
                qualifying_orthogroups, alignment_dir, conserved_fnn_dir, codon_dir, threads
            )
            logging.info(f"Codon alignments created")
        except Exception as e:
            logging.error(f"Error creating codon alignments: {e}")
            raise typer.Exit(code=1)
    
    logging.info(f"Pipeline completed successfully. Results in: {output_dir}")


# Include all the helper functions from the original script
# (filter_and_write_fasta_sequences, run_prodigal_parallel, etc.)
# These remain largely unchanged...

def filter_and_write_fasta_sequences(fasta_path: str, matching_seq_names: Set[str], output_fna_dir: str):
    """Parse the FASTA file and write matching records into separate FASTA files."""
    logging.info(f"Creating output FASTA directory: {output_fna_dir}")
    os.makedirs(output_fna_dir, exist_ok=True)

    prefix_counts = defaultdict(int)
    total_written = 0

    with open(fasta_path, 'r') as infile:
        for record in SeqIO.parse(infile, 'fasta'):
            if record.id in matching_seq_names:
                if '|' in record.id:
                    prefix, contigid = record.id.split('|', 1)
                else:
                    prefix = record.id
                    contigid = ""
                prefix_counts[prefix] += 1
                index = prefix_counts[prefix]
                output_filename = f"{prefix}_{index}.fasta"
                output_path = os.path.join(output_fna_dir, output_filename)
                new_id = f"{prefix}_{index}|{contigid}"
                new_record = SeqRecord(
                    Seq(str(record.seq)),
                    id=new_id,
                    description=""
                )
                with open(output_path, 'w') as outfile:
                    SeqIO.write(new_record, outfile, 'fasta')
                total_written += 1
                if total_written % 1000 == 0:
                    logging.info(f"{total_written} sequences written.")

    logging.info(f"Successfully wrote {total_written} sequences to '{output_fna_dir}'.")


def run_prodigal_on_fna(fna_path: str, faa_dir: str, fnn_dir: str, base_name: str) -> bool:
    """Run Prodigal-gv on a single .fna file to predict genes."""
    faa_output = os.path.join(faa_dir, f"{base_name}.faa")
    fnn_output = os.path.join(fnn_dir, f"{base_name}.fnn")

    try:
        result = subprocess.run(
            [
                "prodigal-gv",
                "-i", fna_path,
                "-a", faa_output,
                "-d", fnn_output,
                "-p", "meta",
                "-q"
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logging.info(f"Prodigal-gv successfully ran on '{fna_path}'")
        return True
    except subprocess.CalledProcessError as e:
        error_message = e.stderr.decode().strip()
        logging.error(f"Error running Prodigal-gv on '{fna_path}': {error_message}")
        return False


def run_prodigal_parallel(fna_dir: str, faa_dir: str, fnn_dir: str, threads: int) -> bool:
    """Run Prodigal-gv on all .fna files in parallel."""
    logging.info(f"Running Prodigal-gv on .fna files in '{fna_dir}' with {threads} threads.")
    os.makedirs(faa_dir, exist_ok=True)
    os.makedirs(fnn_dir, exist_ok=True)

    fna_files = [f for f in os.listdir(fna_dir) if f.endswith('.fasta') or f.endswith('.fna')]
    total_files = len(fna_files)
    if total_files == 0:
        logging.error(f"No .fasta or .fna files found in '{fna_dir}'.")
        return False

    success_count = 0

    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = {
            executor.submit(
                run_prodigal_on_fna, 
                os.path.join(fna_dir, fna_file), 
                faa_dir, 
                fnn_dir, 
                os.path.splitext(fna_file)[0]
            ): fna_file
            for fna_file in fna_files
        }

        for future in tqdm(as_completed(futures), total=total_files, desc="Running Prodigal-gv", unit="file"):
            fna_file = futures[future]
            try:
                success = future.result()
                if success:
                    success_count += 1
            except Exception as e:
                logging.error(f"Unhandled exception while processing '{fna_file}': {e}")

    logging.info(f"Prodigal-gv completed successfully on {success_count}/{total_files} files.")
    return success_count == total_files


# Include all other helper functions from the original script...
# (run_prodigal_adjust_headers, run_proteinortho, create_orthogroups_file, etc.)
# These remain the same as in the original file

def run_prodigal_adjust_headers(faa_dir: str, fnn_dir: str):
    """Adjust headers in Prodigal output files."""
    for subdir, ext in [(faa_dir, '.faa'), (fnn_dir, '.fnn')]:
        for filename in os.listdir(subdir):
            if filename.endswith(ext):
                file_path = os.path.join(subdir, filename)
                genome_id = filename.replace(ext, '')
                
                records = []
                for record in SeqIO.parse(file_path, "fasta"):
                    original_id = record.id
                    if '|' not in record.id:
                        record.id = f"{genome_id}|{original_id}"
                        record.description = ""
                    records.append(record)
                
                SeqIO.write(records, file_path, "fasta")


def run_proteinortho(proteinortho_dir: str, project_name: str, faa_dir: str, threads: int):
    """Run Proteinortho on all .faa files."""
    logging.info("Running Proteinortho...")
    
    faa_files = [os.path.join(faa_dir, f) for f in os.listdir(faa_dir) if f.endswith('.faa')]
    
    if not faa_files:
        logging.error(f"No .faa files found in '{faa_dir}'.")
        raise ValueError("No input files for Proteinortho")
    
    cmd = [
        "proteinortho",
        "-project", os.path.join(proteinortho_dir, os.path.basename(project_name)),
        "-cpus", str(threads)
    ] + faa_files
    
    try:
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        logging.info("Proteinortho completed successfully.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Proteinortho failed: {e.stderr}")
        raise


def create_orthogroups_file(proteinortho_tsv: str, output_file: str):
    """Create orthogroups file from Proteinortho output."""
    with open(proteinortho_tsv, 'r') as infile, open(output_file, 'w') as outfile:
        header = infile.readline().strip().split('\t')[3:]  # Skip initial columns
        for i, line in enumerate(infile):
            parts = line.strip().split('\t')
            orthogroup = f"og{i+1}"
            proteins = []
            for genome, protein_list in zip(header, parts[3:]):
                genome_id = genome.split('|')[0].replace(".faa", "")
                if protein_list and not protein_list.startswith('*'):
                    for protein in protein_list.split(','):
                        protein_id = protein.split('|')[-1]
                        proteins.append(f"{genome_id}|{protein_id}")
            if proteins:
                outfile.write(f"{orthogroup} {' '.join(proteins)}\n")


def filter_orthogroups_by_conservation(orthogroups_file: str, conservation_level: float, total_genomes: int) -> List[str]:
    """Filter orthogroups by conservation level."""
    qualifying_orthogroups = []
    with open(orthogroups_file, 'r') as infile:
        for line in infile:
            parts = line.strip().split()
            orthogroup = parts[0]
            unique_genomes = set(part.split('|')[0] for part in parts[1:])
            if len(unique_genomes) >= round(conservation_level * total_genomes):
                qualifying_orthogroups.append(orthogroup)
    return qualifying_orthogroups


def create_multi_faa_fnn_files(
    qualifying_orthogroups: List[str], 
    orthogroups_file: str,
    source_faa_dir: str, 
    source_fnn_dir: str,
    target_faa_dir: str, 
    target_fnn_dir: str,
    threads: int
):
    """Create multi-FAA and FNN files for conserved orthogroups."""
    
    with open(orthogroups_file, 'r') as infile:
        orthogroup_data = {line.split()[0]: line.strip().split()[1:] for line in infile}
    
    def process_og(args):
        og_id, proteins, src_faa_dir, src_fnn_dir, tgt_faa_dir, tgt_fnn_dir = args
        
        faa_records = []
        fnn_records = []
        
        for protein_info in proteins:
            genome_id, protein_id = protein_info.split('|', 1)
            
            # Read FAA
            faa_file = os.path.join(src_faa_dir, f"{genome_id}.faa")
            if os.path.exists(faa_file):
                for record in SeqIO.parse(faa_file, "fasta"):
                    if record.id == protein_info or record.id.endswith(f"|{protein_id}"):
                        faa_records.append(record)
                        break
            
            # Read FNN
            fnn_file = os.path.join(src_fnn_dir, f"{genome_id}.fnn")
            if os.path.exists(fnn_file):
                for record in SeqIO.parse(fnn_file, "fasta"):
                    if protein_id in record.id:
                        fnn_records.append(record)
                        break
        
        # Write multi-FAA
        if faa_records:
            faa_output = os.path.join(tgt_faa_dir, f"{og_id}.faa")
            SeqIO.write(faa_records, faa_output, "fasta")
        
        # Write multi-FNN
        if fnn_records:
            fnn_output = os.path.join(tgt_fnn_dir, f"{og_id}.fnn")
            SeqIO.write(fnn_records, fnn_output, "fasta")
    
    # Prepare arguments
    args_list = [
        (og_id, orthogroup_data[og_id], source_faa_dir, source_fnn_dir, target_faa_dir, target_fnn_dir)
        for og_id in qualifying_orthogroups if og_id in orthogroup_data
    ]
    
    # Process in parallel
    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = [executor.submit(process_og, args) for args in args_list]
        for future in tqdm(as_completed(futures), total=len(futures), desc="Creating Multi-FAA/FNN Files"):
            try:
                future.result()
            except Exception as e:
                logging.error(f"Error processing orthogroup: {e}")


def align_all_faa_files(source_dir: str, alignment_dir: str, threads: int):
    """Align all FAA files using MAFFT."""
    
    def align_faa_file(faa_file_path: str, alignment_dir: str):
        base_name = os.path.basename(faa_file_path)
        alignment_file_path = os.path.join(alignment_dir, f"{base_name}.mafft")
        
        mafft_cmd = ["mafft", "--auto", faa_file_path]
        
        try:
            with open(alignment_file_path, 'w') as outfile:
                subprocess.run(mafft_cmd, stdout=outfile, stderr=subprocess.DEVNULL, check=True)
        except subprocess.CalledProcessError as e:
            logging.error(f"Error aligning '{faa_file_path}': {e}")
    
    faa_files = [os.path.join(source_dir, f) for f in os.listdir(source_dir) if f.endswith('.faa')]
    
    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = {executor.submit(align_faa_file, faa_file, alignment_dir): faa_file for faa_file in faa_files}
        
        for future in tqdm(as_completed(futures), total=len(faa_files), desc="Aligning .faa Files"):
            try:
                future.result()
            except Exception as e:
                logging.error(f"Unhandled exception: {e}")


def create_codon_alignments_for_orthogroups(
    orthogroups: List[str],
    alignment_dir: str,
    conserved_fnn_dir: str,
    codon_dir: str,
    threads: int
):
    """Create codon alignments for orthogroups."""
    
    def create_codon_alignment_task(og_id: str):
        protein_alignment_file = os.path.join(alignment_dir, f"{og_id}.faa.mafft")
        nucleotide_fnn_file = os.path.join(conserved_fnn_dir, f"{og_id}.fnn")
        codon_alignment_file = os.path.join(codon_dir, f"{og_id}.codon")
        
        if not os.path.exists(protein_alignment_file) or not os.path.exists(nucleotide_fnn_file):
            return
        
        # Load protein alignment
        protein_records = list(SeqIO.parse(protein_alignment_file, "fasta"))
        if not protein_records:
            return
        
        # Load nucleotide sequences
        nucleotide_records = {}
        for record in SeqIO.parse(nucleotide_fnn_file, "fasta"):
            if '|' in record.id:
                genome_id, protein_id = record.id.split('|', 1)
                nucleotide_records[protein_id] = record.seq
            else:
                nucleotide_records[record.id] = record.seq
        
        # Create codon alignment
        codon_alignment = []
        
        for protein_record in protein_records:
            protein_seq = str(protein_record.seq)
            
            # Find matching nucleotide sequence
            nuc_seq = None
            if '|' in protein_record.id:
                _, protein_id = protein_record.id.split('|', 1)
                nuc_seq = nucleotide_records.get(protein_id)
            
            if not nuc_seq:
                nuc_seq = nucleotide_records.get(protein_record.id)
            
            if not nuc_seq:
                continue
            
            # Build codon alignment
            codon_seq = []
            nuc_index = 0
            
            for aa in protein_seq:
                if aa == '-':
                    codon_seq.append('---')
                else:
                    if nuc_index + 3 <= len(nuc_seq):
                        codon_seq.append(str(nuc_seq[nuc_index:nuc_index+3]))
                        nuc_index += 3
                    else:
                        break
            
            if codon_seq:
                codon_record = SeqRecord(
                    Seq(''.join(codon_seq)),
                    id=protein_record.id,
                    description=""
                )
                codon_alignment.append(codon_record)
        
        # Write codon alignment
        if codon_alignment:
            SeqIO.write(codon_alignment, codon_alignment_file, "fasta")
    
    # Process in parallel
    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = [executor.submit(create_codon_alignment_task, og_id) for og_id in orthogroups]
        
        for future in tqdm(as_completed(futures), total=len(futures), desc="Creating Codon Alignments"):
            try:
                future.result()
            except Exception as e:
                logging.error(f"Error creating codon alignment: {e}")


if __name__ == "__main__":
    app()