#!/usr/bin/env python3

import os
import csv
import shutil
import subprocess
import re  # Import re for regex operations
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
from Bio.Seq import Seq
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import logging
import typer
from typing import Optional, List, Set

app = typer.Typer()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("pipeline.log"),  # Log to file
        logging.StreamHandler()               # Also log to console
    ]
)

def collect_matching_seq_names(tsv_path: str, target_taxonomy: str, use_regex: bool = False, limit: Optional[int] = None) -> Set[str]:
    """
    Read the TSV file and collect sequence names where the taxonomy matches the target_taxonomy.
    Supports exact matching or regex-based matching based on the use_regex flag.
    Optionally limits the number of sequence names collected to 'limit'.

    Args:
        tsv_path (str): Path to the taxonomy annotation TSV file.
        target_taxonomy (str): Taxonomy string or regex pattern to filter by.
        use_regex (bool): If True, treats target_taxonomy as a regex pattern.
        limit (Optional[int]): Maximum number of sequence names to collect. If None, no limit.

    Returns:
        Set[str]: A set of matching sequence names.
    """
    logging.info(f"Reading TSV file: {tsv_path}")
    matching_seq_names = set()
    sequences_collected = 0

    with open(tsv_path, 'r', newline='', encoding='utf-8') as tsvfile:
        reader = csv.reader(tsvfile, delimiter='\t')
        header = next(reader, None)  # Skip header
        if header is None:
            raise ValueError("The TSV file is empty or missing a header.")

        taxonomy_idx = len(header) - 1  # Assuming taxonomy is the last column
        seq_name_idx = 0  # Assuming sequence name is the first column

        # Compile regex pattern if use_regex is True
        if use_regex:
            try:
                pattern = re.compile(target_taxonomy)
                logging.info(f"Using regex pattern for matching: '{target_taxonomy}'")
            except re.error as e:
                logging.error(f"Invalid regex pattern: {e}")
                raise typer.Exit(code=1)
        else:
            logging.info(f"Filtering sequences with exact taxonomy match: '{target_taxonomy}'")

        for row_num, row in enumerate(reader, start=2):
            if len(row) < taxonomy_idx + 1:
                logging.warning(f"Row {row_num} is incomplete. Skipping.")
                continue
            taxonomy = row[taxonomy_idx].strip()
            match = False
            if use_regex:
                if pattern.search(taxonomy):
                    match = True
            else:
                if taxonomy == target_taxonomy:
                    match = True

            if match:
                seq_name = row[seq_name_idx].strip()
                if seq_name not in matching_seq_names:
                    matching_seq_names.add(seq_name)
                    sequences_collected += 1
                    if limit is not None and sequences_collected >= limit:
                        logging.info(f"Reached the specified limit of {limit} sequences.")
                        break

    logging.info(f"Found {len(matching_seq_names)} matching sequences.")
    return matching_seq_names

def filter_and_write_fasta_sequences(fasta_path: str, matching_seq_names: Set[str], output_fna_dir: str):
    """
    Parse the FASTA file and write matching records into separate FASTA files.
    """
    logging.info(f"Creating output FASTA directory: {output_fna_dir}")
    os.makedirs(output_fna_dir, exist_ok=True)

    prefix_counts = defaultdict(int)
    total_written = 0

    with open(fasta_path, 'r') as infile:
        for record in SeqIO.parse(infile, 'fasta'):
            if record.id in matching_seq_names:
                if '|' in record.id:
                    prefix, contigid = record.id.split('|', 1)
                else:
                    prefix = record.id
                    contigid = ""
                prefix_counts[prefix] += 1
                index = prefix_counts[prefix]
                output_filename = f"{prefix}_{index}.fasta"  # Modified to include the index
                output_path = os.path.join(output_fna_dir, output_filename)
                # Directly append contigid without adding another 'NODE_'
                new_id = f"{prefix}_{index}|{contigid}"  # **Modified Line**
                new_record = SeqRecord(
                    Seq(str(record.seq)),
                    id=new_id,
                    description=""
                )
                with open(output_path, 'w') as outfile:
                    SeqIO.write(new_record, outfile, 'fasta')
                total_written += 1
                if total_written % 1000 == 0:
                    logging.info(f"{total_written} sequences written.")

    logging.info(f"Successfully wrote {total_written} sequences to '{output_fna_dir}'.")

def run_prodigal_on_fna(fna_path: str, faa_dir: str, fnn_dir: str, base_name: str) -> bool:
    """
    Run Prodigal on a single .fna file to predict genes.
    Generates .faa and .fnn files.
    """
    faa_output = os.path.join(faa_dir, f"{base_name}.faa")
    fnn_output = os.path.join(fnn_dir, f"{base_name}.fnn")

    try:
        result = subprocess.run(
            [
                "prodigal-gv",
                "-i", fna_path,
                "-a", faa_output,
                "-d", fnn_output,
                "-p", "meta",
                "-q"
            ],
            check=True,
            stdout=subprocess.PIPE,  # Capture stdout
            stderr=subprocess.PIPE   # Capture stderr
        )
        logging.info(f"Prodigal successfully ran on '{fna_path}'. Outputs: '{faa_output}', '{fnn_output}'.")
        return True
    except subprocess.CalledProcessError as e:
        error_message = e.stderr.decode().strip()
        logging.error(f"Error running Prodigal on '{fna_path}': {error_message}")
        return False

def run_prodigal_parallel(fna_dir: str, faa_dir: str, fnn_dir: str, threads: int) -> bool:
    """
    Run Prodigal on all .fna files in parallel.
    """
    logging.info(f"Running Prodigal on .fna files in '{fna_dir}' with {threads} threads.")
    os.makedirs(faa_dir, exist_ok=True)
    os.makedirs(fnn_dir, exist_ok=True)

    fna_files = [f for f in os.listdir(fna_dir) if f.endswith('.fasta') or f.endswith('.fna')]
    total_files = len(fna_files)
    if total_files == 0:
        logging.error(f"No .fasta or .fna files found in '{fna_dir}'. Exiting.")
        return False

    success_count = 0

    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = {
            executor.submit(run_prodigal_on_fna, os.path.join(fna_dir, fna_file), faa_dir, fnn_dir, os.path.splitext(fna_file)[0]): fna_file
            for fna_file in fna_files
        }

        for future in tqdm(as_completed(futures), total=total_files, desc="Running Prodigal", unit="file"):
            fna_file = futures[future]
            try:
                result = future.result()
                if result:
                    success_count += 1
            except Exception as e:
                logging.error(f"Unhandled exception for '{fna_file}': {e}")

    logging.info(f"Prodigal run completed: {success_count}/{total_files} succeeded.")

    return success_count == total_files

def adjust_headers(prod_output_path: str, delimiter: str = "|"):
    """
    Adjust the headers in Prodigal output files (.faa or .fnn) to use the specified delimiter.
    For example, replaces the first occurrence of '_NODE_' with '|NODE_'.
    """
    temp_output_path = prod_output_path + '.tmp'
    try:
        with open(prod_output_path, 'r') as infile, open(temp_output_path, 'w') as outfile:
            for record in SeqIO.parse(infile, 'fasta'):
                if '_NODE_' in record.id:
                    new_id = record.id.replace('_NODE_', f'{delimiter}NODE_', 1)
                else:
                    new_id = record.id  # Keep as is if '_NODE_' not present
                # Remove asterisks from sequence
                clean_seq = Seq(str(record.seq).replace("*",""))
                new_record = SeqRecord(
                    clean_seq,
                    id=new_id,
                    description=""
                )
                SeqIO.write(new_record, outfile, 'fasta')
        os.replace(temp_output_path, prod_output_path)
        logging.info(f"Adjusted headers in '{prod_output_path}'.")
    except Exception as e:
        logging.error(f"Error adjusting headers in '{prod_output_path}': {e}")

def run_prodigal_adjust_headers(faa_dir: str, fnn_dir: str):
    """
    Adjust headers in all .faa and .fnn files.
    """
    logging.info("Adjusting headers in Prodigal output files.")

    # Adjust headers for .faa files
    faa_files = [f for f in os.listdir(faa_dir) if f.endswith('.faa')]
    for faa_file in tqdm(faa_files, desc="Adjusting .faa headers", unit="file"):
        faa_path = os.path.join(faa_dir, faa_file)
        adjust_headers(faa_path)

    # Adjust headers for .fnn files
    fnn_files = [f for f in os.listdir(fnn_dir) if f.endswith('.fnn')]
    for fnn_file in tqdm(fnn_files, desc="Adjusting .fnn headers", unit="file"):
        fnn_path = os.path.join(fnn_dir, fnn_file)
        adjust_headers(fnn_path)

def run_proteinortho(proteinortho_dir: str, taxonomy_safe: str, faa_dir: str, threads: int):
    """
    Run Proteinortho and move output files to the designated directory.
    """
    logging.info("Running Proteinortho...")
    proteinortho_cmd = f"proteinortho -cpus={threads} -project={taxonomy_safe} {faa_dir}/*.faa"
    try:
        subprocess.run(proteinortho_cmd, shell=True, check=True)
        print ("done")
        
        # Move all Proteinortho output files to proteinortho_dir
        moved_files = False
        for file in os.listdir('.'):
            if file.startswith(f"{taxonomy_safe}.") and not os.path.isdir(file):
                src_path = os.path.join('.', file)
                dst_path = os.path.join(proteinortho_dir, file)
                # Check if destination exists to avoid race condition
                if os.path.exists(dst_path):
                    logging.warning(f"Destination file '{dst_path}' already exists. Skipping move.")
                    continue
                try:
                    shutil.move(src_path, dst_path)
                    logging.info(f"Moved '{file}' to '{proteinortho_dir}'.")
                    moved_files = True
                except (shutil.Error, OSError) as e:
                    logging.error(f"Failed to move '{file}': {e}")
                    # Try to clean up if partial move occurred
                    if os.path.exists(dst_path) and os.path.exists(src_path):
                        os.remove(src_path)
        if not moved_files:
            logging.warning(f"No Proteinortho output files found starting with '{taxonomy_safe}.proteinortho'.")
        else:
            logging.info("Proteinortho run completed successfully.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Error running Proteinortho: {e}")
        raise typer.Exit(code=1)

def create_orthogroups_file(proteinortho_tsv: str, output_file: str):
    """
    Parse Proteinortho TSV file and create orthogroups.
    """
    with open(proteinortho_tsv, 'r') as infile, open(output_file, 'w') as outfile:
        header = infile.readline().strip().split('\t')[3:]  # Skip initial columns
        for i, line in enumerate(infile):
            parts = line.strip().split('\t')
            orthogroup = f"og{i+1}"
            proteins = []
            for genome, protein_list in zip(header, parts[3:]):
                # Keep the .n suffix for the genome ID
                genome_id = genome.split('|')[0].replace(".faa", "")

                if protein_list and not protein_list.startswith('*'):
                    for protein in protein_list.split(','):
                        # Extract the correct protein ID
                        protein_id = protein.split('|')[-1]
                        proteins.append(f"{genome_id}|{protein_id}")
            if proteins:
                outfile.write(f"{orthogroup} {' '.join(proteins)}\n")
    logging.info(f"Orthogroups file created at '{output_file}'.")

def filter_orthogroups_by_conservation(orthogroups_file: str, conservation_level: float, total_genomes: int) -> List[str]:
    """
    Filter orthogroups based on the conservation level.
    
    Args:
        orthogroups_file (str): Path to the orthogroups groups file.
        conservation_level (float): The required conservation level (e.g., 1.0 for fully conserved).
        total_genomes (int): The total number of genomes considered.

    Returns:
        List[str]: A list of orthogroup IDs that meet the conservation criteria.
    """
    qualifying_orthogroups = []
    required_genomes = int(total_genomes * conservation_level)

    logging.info(f"Filtering orthogroups by conservation level: {conservation_level} ({required_genomes}/{total_genomes} genomes)")

    with open(orthogroups_file, 'r') as infile:
        for line in infile:
            parts = line.strip().split(' ')
            og_id = parts[0]
            proteins = parts[1:]
            # Determine unique genomes represented in this orthogroup
            genomes_in_og = set()
            for protein in proteins:
                genome_id = protein.split('|')[0]
                genomes_in_og.add(genome_id)
            if len(genomes_in_og) >= required_genomes:
                qualifying_orthogroups.append(og_id)

    return qualifying_orthogroups

def process_og(args):
    """
    Process a single orthogroup: extract FAA and FNN records and write them to respective files.
    """
    og_id, proteins, src_faa_dir, src_fnn_dir, target_faa_dir, target_fnn_dir = args

    faa_records = []
    fnn_records = []

    for protein in proteins:
        try:
            genome_id, protein_id = protein.split('|')
        except ValueError:
            logging.warning(f"Protein ID '{protein}' does not contain '|'. Skipping.")
            continue

        faa_file_path = os.path.join(src_faa_dir, f"{genome_id}.faa")
        fnn_file_path = os.path.join(src_fnn_dir, f"{genome_id}.fnn")

        # Fetch FAA record
        if os.path.exists(faa_file_path):
            for record in SeqIO.parse(faa_file_path, 'fasta'):
                if record.id == protein:
                    faa_records.append(record)
                    break

        # Fetch FNN record
        if os.path.exists(fnn_file_path):
            for record in SeqIO.parse(fnn_file_path, 'fasta'):
                if record.id == protein:  # Updated to match the full ID
                    fnn_records.append(record)
                    break

    # Write FAA records
    faa_out_path = os.path.join(target_faa_dir, f"{og_id}.faa")
    if faa_records:
        with open(faa_out_path, 'w') as faa_out:
            SeqIO.write(faa_records, faa_out, 'fasta')

    # Write FNN records
    fnn_out_path = os.path.join(target_fnn_dir, f"{og_id}.fnn")
    if fnn_records:
        with open(fnn_out_path, 'w') as fnn_out:
            SeqIO.write(fnn_records, fnn_out, 'fasta')

def create_multi_faa_fnn_files(
    orthogroups: List[str],
    orthogroups_file: str,
    src_faa_dir: str,
    src_fnn_dir: str,
    target_faa_dir: str,
    target_fnn_dir: str,
    threads: int
):
    """
    Create multi-FAA and FNN files for conserved orthogroups.
    """
    os.makedirs(target_faa_dir, exist_ok=True)
    os.makedirs(target_fnn_dir, exist_ok=True)

    # Parse orthogroups file to map orthogroup to proteins
    og_to_proteins = {}
    with open(orthogroups_file, 'r') as infile:
        for line in infile:
            parts = line.strip().split(' ')
            if len(parts) < 2:
                continue  # Skip orthogroups without proteins
            og_id = parts[0]
            proteins = parts[1:]
            if og_id in orthogroups:
                og_to_proteins[og_id] = proteins

    logging.info(f"Creating multi-FAA and FNN files for {len(og_to_proteins)} orthogroups.")

    # Prepare arguments for each orthogroup
    args_list = [
        (og_id, proteins, src_faa_dir, src_fnn_dir, target_faa_dir, target_fnn_dir)
        for og_id, proteins in og_to_proteins.items()
    ]

    # Use ProcessPoolExecutor for parallel processing
    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = [
            executor.submit(process_og, args) for args in args_list
        ]
        for future in tqdm(as_completed(futures), total=len(futures), desc="Creating Multi-FAA/FNN Files", unit="og"):
            try:
                future.result()  # This will raise any exceptions encountered during execution
            except Exception as e:
                logging.error(f"Error processing orthogroup: {e}")

def align_faa_file(faa_file_path: str, alignment_dir: str):
    """
    Aligns a single .faa file using MAFFT and saves the alignment.
    """
    base_name = os.path.basename(faa_file_path)
    alignment_file_name = f"{base_name}.mafft"
    alignment_file_path = os.path.join(alignment_dir, alignment_file_name)

    # Command to run MAFFT
    mafft_cmd = ["mafft", "--auto", faa_file_path]

    # Run MAFFT and write output to the alignment file
    try:
        with open(alignment_file_path, 'w') as outfile:
            subprocess.run(mafft_cmd, stdout=outfile, stderr=subprocess.DEVNULL, check=True)
        logging.info(f"Alignment completed for '{faa_file_path}'.")
    except subprocess.CalledProcessError as e:
        logging.error(f"Error aligning '{faa_file_path}': {e}")

def align_all_faa_files(source_dir: str, alignment_dir: str, threads: int):
    """
    Aligns all .faa files in the source directory using MAFFT and multiprocessing,
    with a progress bar.
    """
    logging.info(f"Aligning all .faa files in '{source_dir}' with MAFFT.")
    os.makedirs(alignment_dir, exist_ok=True)

    faa_files = [os.path.join(source_dir, f) for f in os.listdir(source_dir) if f.endswith('.faa')]
    total_files = len(faa_files)

    if total_files == 0:
        logging.warning(f"No .faa files found in '{source_dir}'. Skipping alignment step.")
        return

    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = {executor.submit(align_faa_file, faa_file, alignment_dir): faa_file for faa_file in faa_files}

        for future in tqdm(as_completed(futures), total=total_files, desc="Aligning .faa Files", unit="file"):
            faa_file = futures[future]
            try:
                future.result()
            except Exception as e:
                logging.error(f"Unhandled exception while aligning '{faa_file}': {e}")

def create_codon_alignment_task(og_id: str, alignment_dir: str, conserved_fnn_dir: str, codon_dir: str):
    """
    Create codon alignment for a single orthogroup.
    """
    protein_alignment_file = os.path.join(alignment_dir, f"{og_id}.faa.mafft")
    nucleotide_fnn_file = os.path.join(conserved_fnn_dir, f"{og_id}.fnn")
    codon_alignment_file = os.path.join(codon_dir, f"{og_id}.codon")

    if not os.path.exists(protein_alignment_file):
        logging.warning(f"Protein alignment file '{protein_alignment_file}' not found. Skipping.")
        return

    if not os.path.exists(nucleotide_fnn_file):
        logging.warning(f"Nucleotide FNN file '{nucleotide_fnn_file}' not found. Skipping.")
        return

    # Load protein alignment
    protein_records = list(SeqIO.parse(protein_alignment_file, "fasta"))
    if not protein_records:
        logging.warning(f"No records found in '{protein_alignment_file}'. Skipping.")
        return

    # Load nucleotide sequences
    nucleotide_records = {}
    for record in SeqIO.parse(nucleotide_fnn_file, "fasta"):
        try:
            genome_id, protein_id = record.id.split('|')
            nucleotide_records[protein_id] = record.seq
        except ValueError:
            logging.warning(f"FNN record ID '{record.id}' does not contain '|'. Skipping.")
            continue

    # Create codon alignment
    codon_alignment = []
    for protein_record in protein_records:
        try:
            genome_id, protein_id = protein_record.id.split('|')
        except ValueError:
            logging.warning(f"Protein alignment record ID '{protein_record.id}' does not contain '|'. Skipping.")
            continue

        nucleotide_seq = nucleotide_records.get(protein_id)
        if nucleotide_seq:
            codon_seq = ""
            seq_pointer = 0  # To track nucleotide sequence position
            for aa in protein_record.seq:
                if aa == '-':
                    codon_seq += '---'
                else:
                    if seq_pointer + 3 > len(nucleotide_seq):
                        logging.warning(f"Not enough nucleotides for protein '{protein_id}'. Skipping remaining amino acids.")
                        codon_seq += '---' * (len(protein_record.seq) - len(codon_seq)//3)
                        break
                    codon_seq += str(nucleotide_seq[seq_pointer:seq_pointer+3])
                    seq_pointer += 3
            codon_alignment.append(SeqRecord(Seq(codon_seq), id=protein_record.id, description=""))

    # Write codon alignment
    if codon_alignment:
        with open(codon_alignment_file, 'w') as outfile:
            SeqIO.write(codon_alignment, outfile, "fasta")
        logging.info(f"Codon alignment created for orthogroup '{og_id}'.")
    else:
        logging.warning(f"No codon sequences created for orthogroup '{og_id}'.")

def create_codon_alignments_for_orthogroups(
    orthogroups: List[str],
    alignment_dir: str,
    conserved_fnn_dir: str,
    codon_dir: str,
    threads: int
):
    """
    Create codon alignments for all conserved orthogroups using multiprocessing.
    """
    logging.info(f"Creating codon alignments in '{codon_dir}'.")
    os.makedirs(codon_dir, exist_ok=True)

    if not orthogroups:
        logging.warning("No orthogroups to process for codon alignments.")
        return

    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = [
            executor.submit(create_codon_alignment_task, og_id, alignment_dir, conserved_fnn_dir, codon_dir)
            for og_id in orthogroups
        ]

    # Progress bar
    for future in tqdm(as_completed(futures), total=len(futures), desc="Creating Codon Alignments", unit="og"):
        try:
            future.result()
        except Exception as e:
            logging.error(f"Unhandled exception while creating codon alignment: {e}")

@app.command()
def main(
    tsv_path: str = typer.Option(..., "-t", "--tsv", help="Path to the taxonomy annotation TSV file."),
    fasta_path: str = typer.Option(..., "-f", "--fna", help="Path to the input FASTA (.fna) file."),
    taxonomy: str = typer.Option(..., "-x", "--taxonomy", help="Taxonomy string or regex pattern to filter by (e.g., 'Viruses-Duplodnaviria-Heunggongvirae'). Use regex syntax."),
    conservation_level: float = typer.Option(0.3, "-c", "--conservation", help="Conservation level for orthogroup filtering (e.g., 1.0 for fully conserved)."),
    threads: int = typer.Option(4, "-j", "--threads", help="Number of threads for parallel processing."),
    use_regex: bool = typer.Option(False, "-r", "--regex", help="Enable regex-based taxonomy matching."),
    n: Optional[int] = typer.Option(None, "-n", "--number", help="Limit the sampling of genomes to the specified number.")
):
    """
    Pipeline to filter sequences based on taxonomy, predict genes with Prodigal,
    identify orthogroups with Proteinortho, align conserved orthogroups with MAFFT,
    and create codon alignments.
    """
    # Step 1: Collect matching sequence names
    matching_seq_names = collect_matching_seq_names(tsv_path, taxonomy, use_regex, limit=n)
    if not matching_seq_names:
        logging.error("No matching sequences found. Exiting.")
        raise typer.Exit(code=1)

    # Step 2: Filter and write .fna files
    taxonomy_safe = taxonomy.replace(';', '-').replace(' ', '_')  # Replace spaces to avoid directory issues
    fna_dir = os.path.join(taxonomy_safe, "fna")
    filter_and_write_fasta_sequences(fasta_path, matching_seq_names, fna_dir)

    # Step 3: Run Prodigal on all .fna files
    faa_dir = os.path.join(taxonomy_safe, "faa")
    fnn_dir = os.path.join(taxonomy_safe, "fnn")
    prodigal_success = run_prodigal_parallel(fna_dir, faa_dir, fnn_dir, threads)

    if not prodigal_success:
        logging.error("Prodigal failed on some or all .fna files. Aborting pipeline.")
        raise typer.Exit(code=1)

    # Step 4: Adjust Prodigal output headers
    run_prodigal_adjust_headers(faa_dir, fnn_dir)

    # Step 5: Run Proteinortho
    proteinortho_dir = os.path.join(taxonomy_safe, "proteinortho")
    os.makedirs(proteinortho_dir, exist_ok=True)

    run_proteinortho(proteinortho_dir, taxonomy_safe, faa_dir, threads)

    # Step 6: Parse Proteinortho Output to Create Orthogroups File
    proteinortho_tsv = os.path.join(proteinortho_dir, f"{taxonomy_safe}.proteinortho.tsv")
    orthogroups_file = os.path.join(proteinortho_dir, f"{taxonomy_safe}.proteinortho.groups")

    if not os.path.exists(proteinortho_tsv):
        logging.error(f"Proteinortho TSV file '{proteinortho_tsv}' not found. Aborting pipeline.")
        raise typer.Exit(code=1)

    try:
        create_orthogroups_file(proteinortho_tsv, orthogroups_file)
    except Exception as e:
        logging.error(f"Error creating orthogroups file: {e}")
        raise typer.Exit(code=1)

    # Step 7: Filter Orthogroups by Conservation
    qualifying_orthogroups = filter_orthogroups_by_conservation(orthogroups_file, conservation_level, len(matching_seq_names))
    logging.info(f"Found {len(qualifying_orthogroups)} orthogroups meeting the conservation level {conservation_level}.")

    if conservation_level > 0 and qualifying_orthogroups:
        conserved_faa_dir = os.path.join(taxonomy_safe, "conserved_faa")
        conserved_fnn_dir = os.path.join(taxonomy_safe, "conserved_fnn")
        os.makedirs(conserved_faa_dir, exist_ok=True)
        os.makedirs(conserved_fnn_dir, exist_ok=True)

        # Step 8: Create Multi-FAA and FNN Files for Conserved Orthogroups
        try:
            create_multi_faa_fnn_files(qualifying_orthogroups, orthogroups_file, faa_dir, fnn_dir, conserved_faa_dir, conserved_fnn_dir, threads)
            logging.info(f"Multi-FAA and FNN files created in '{conserved_faa_dir}' and '{conserved_fnn_dir}'.")
        except Exception as e:
            logging.error(f"Error creating multi-FAA and FNN files: {e}")
            raise typer.Exit(code=1)

        # Step 9: Align Conserved Orthogroups with MAFFT
        alignment_dir = os.path.join(taxonomy_safe, "alignments")
        os.makedirs(alignment_dir, exist_ok=True)
        try:
            align_all_faa_files(conserved_faa_dir, alignment_dir, threads)
            logging.info(f"Alignments created in '{alignment_dir}'.")
        except Exception as e:
            logging.error(f"Error creating alignments: {e}")
            raise typer.Exit(code=1)

        # Step 10: Create Codon Alignments
        codon_dir = os.path.join(taxonomy_safe, "codon_alignments")
        os.makedirs(codon_dir, exist_ok=True)
        try:
            create_codon_alignments_for_orthogroups(qualifying_orthogroups, alignment_dir, conserved_fnn_dir, codon_dir, threads)
            logging.info(f"Codon alignments created in '{codon_dir}'.")
        except Exception as e:
            logging.error(f"Error creating codon alignments: {e}")
            raise typer.Exit(code=1)

    logging.info("Pipeline completed successfully.")

if __name__ == "__main__":
    app()