[project]
name = "ppdesign"
version = "0.2.0"
description = "Modern bioinformatics pipeline for oligonucleotide probe and primer design"
authors = ["PPDesign Development Team"]
channels = ["conda-forge", "bioconda"]
platforms = ["linux-64", "osx-64", "osx-arm64"]

[tasks]
# Main pipeline tasks
design-unified = "python src/ppdesign/probedesign_unified.py"
design-nucleotide = "python src/ppdesign/probedesign_nucleotide.py"
select-oligos = "python src/ppdesign/probedesign_seqselection.py"
rank-probes = "python src/ppdesign/probedesign_rank.py"
count-kmers = "python src/ppdesign/kmer_finder.py"

# Development tasks
test = "pytest tests/ -v"
test-cov = "pytest tests/ --cov=ppdesign --cov-report=html"
lint = "ruff check src/"
format = "ruff format src/"
type-check = "mypy src/"
docs = "mkdocs serve"
build-docs = "mkdocs build"

# Utility tasks
clean = "rm -rf build/ dist/ *.egg-info/ .coverage htmlcov/ .pytest_cache/ .mypy_cache/ .ruff_cache/"
install-dev = "pip install -e ."

[dependencies]
# Core scientific computing
python = ">=3.11,<3.13"
numpy = ">=1.26"
pandas = ">=2.2"
polars = ">=0.20"
biopython = ">=1.83"
duckdb = ">=0.10"

# Bioinformatics tools
blast = ">=2.15"
diamond = ">=2.1"
mafft = ">=7.520"
muscle = ">=5.1"
minimap2 = ">=2.26"
proteinortho = ">=6.3"
prodigal-gv = ">=2.11"
cd-hit = ">=4.8"
mmseqs2 = ">=15.0"

# Visualization
matplotlib = ">=3.8"

# CLI and utilities
typer = ">=0.9"
tqdm = ">=4.66"
click = ">=8.1"
rich = ">=13.7"
requests = ">=2.31"

# Development dependencies
[feature.dev.dependencies]
pytest = ">=7.4"
pytest-cov = ">=4.1"
ruff = ">=0.3"
mypy = ">=1.9"
black = ">=24.2"
pre-commit = ">=3.6"
mkdocs = ">=1.5"
mkdocs-material = ">=9.5"
mkdocstrings = ">=0.24"
mkdocstrings-python = ">=1.8"

[feature.dev.tasks]
pre-commit-install = "pre-commit install"
pre-commit = "pre-commit run --all-files"

[environments]
default = { solve-group = "default" }
dev = { features = ["dev"], solve-group = "default" }

[activation]
scripts = ["activate.sh"]

[tool.ruff]
line-length = 100
target-version = "py311"

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "UP",   # pyupgrade
    "ARG",  # flake8-unused-arguments
    "SIM",  # flake8-simplify
]
ignore = [
    "E501",  # line too long (handled by formatter)
    "B008",  # do not perform function calls in argument defaults
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
pythonpath = ["src"]

[tool.coverage.run]
source = ["src/ppdesign"]
omit = ["*/tests/*", "*/test_*.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if __name__ == .__main__.:",
    "raise AssertionError",
    "raise NotImplementedError",
]