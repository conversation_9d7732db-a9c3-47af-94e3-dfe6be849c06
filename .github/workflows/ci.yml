name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
        python-version: ["3.11", "3.12"]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Pixi
      uses: prefix-dev/setup-pixi@v0.8.0
      with:
        pixi-version: v0.20.0
    
    - name: Run tests
      run: |
        pixi run test-cov
    
    - name: Run linting
      run: |
        pixi run lint
    
    - name: Run type checking
      run: |
        pixi run type-check
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v4
      if: matrix.os == 'ubuntu-latest' && matrix.python-version == '3.11'
      with:
        file: ./htmlcov/coverage.xml
        fail_ci_if_error: true

  build:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Pixi
      uses: prefix-dev/setup-pixi@v0.8.0
      with:
        pixi-version: v0.20.0
    
    - name: Build package
      run: |
        pixi run python -m build
    
    - name: Check distribution
      run: |
        pixi run twine check dist/*
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dist
        path: dist/